package com.dcas.common.excel;

import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 水印工具测试类
 * 
 * <AUTHOR>
 * @date 2025/7/2
 * @since 1.0.0
 */
@Slf4j
public class WaterMarkUtilTest {
    
    @BeforeEach
    public void setUp() {
        // 打印系统字体信息
        FontTestUtil.printSystemFontInfo();
    }
    
    @Test
    public void testChineseFontDetection() {
        log.info("=== 测试中文字体检测 ===");
        
        // 测试常见字体
        String[] testFonts = {"SimSun", "宋体", "Microsoft YaHei", "微软雅黑", "SansSerif"};
        
        for (String fontName : testFonts) {
            FontTestUtil.testFontChineseSupport(fontName);
        }
    }
    
    @Test
    public void testWatermarkImageGeneration() throws IOException {
        log.info("=== 测试水印图片生成 ===");
        
        String watermarkText = "测试中文水印内容";
        
        // 测试单行水印
        BufferedImage singleLineImage = WaterMarkUtil.createWatermarkImage(watermarkText, false);
        saveTestImage(singleLineImage, "single_line_watermark.png");
        
        // 测试多行水印
        String multiLineText = "第一行测试内容" + System.lineSeparator() + "第二行测试内容";
        BufferedImage multiLineImage = WaterMarkUtil.createWatermarkImage(multiLineText, true);
        saveTestImage(multiLineImage, "multi_line_watermark.png");
        
        log.info("水印图片生成完成，请检查输出文件");
    }
    
    @Test
    public void testExcelWatermark() throws IOException {
        log.info("=== 测试Excel水印添加 ===");
        
        // 创建工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("测试工作表");
        
        // 添加一些测试数据
        sheet.createRow(0).createCell(0).setCellValue("测试数据1");
        sheet.createRow(1).createCell(0).setCellValue("测试数据2");
        sheet.createRow(2).createCell(0).setCellValue("测试数据3");
        
        // 添加水印
        String[] watermarkParams = {"测试水印", "内容正确"};
        WaterMarkUtil.insertWaterMarkTextToXlsx(workbook, sheet, false, watermarkParams);
        
        // 保存文件
        String fileName = "test_watermark.xlsx";
        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            workbook.write(fos);
        }
        
        workbook.close();
        log.info("Excel文件已生成: {}", fileName);
    }
    
    @Test
    public void testMultiLineExcelWatermark() throws IOException {
        log.info("=== 测试多行Excel水印 ===");
        
        // 创建工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("多行水印测试");
        
        // 添加测试数据
        sheet.createRow(0).createCell(0).setCellValue("数据行1");
        sheet.createRow(1).createCell(0).setCellValue("数据行2");
        
        // 添加多行水印
        String[] watermarkParams = {"第一行水印内容", "第二行水印内容", "第三行水印内容"};
        WaterMarkUtil.insertWaterMarkTextToXlsx(workbook, sheet, true, watermarkParams);
        
        // 保存文件
        String fileName = "test_multiline_watermark.xlsx";
        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            workbook.write(fos);
        }
        
        workbook.close();
        log.info("多行水印Excel文件已生成: {}", fileName);
    }
    
    /**
     * 保存测试图片
     */
    private void saveTestImage(BufferedImage image, String fileName) {
        try {
            File outputFile = new File(fileName);
            ImageIO.write(image, "png", outputFile);
            log.info("测试图片已保存: {}", outputFile.getAbsolutePath());
        } catch (IOException e) {
            log.error("保存测试图片失败: {}", e.getMessage());
        }
    }
    
    /**
     * 手动测试方法（可以在main方法中调用）
     */
    public static void manualTest() {
        WaterMarkUtilTest test = new WaterMarkUtilTest();
        
        try {
            test.setUp();
            test.testChineseFontDetection();
            test.testWatermarkImageGeneration();
            test.testExcelWatermark();
            test.testMultiLineExcelWatermark();
        } catch (Exception e) {
            log.error("测试执行失败", e);
        }
    }
    
    public static void main(String[] args) {
        manualTest();
    }
}
