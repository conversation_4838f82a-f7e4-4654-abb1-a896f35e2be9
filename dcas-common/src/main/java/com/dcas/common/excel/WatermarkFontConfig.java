package com.dcas.common.excel;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 水印字体配置类
 * 
 * <AUTHOR>
 * @date 2025/7/2
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dcas.watermark.font")
public class WatermarkFontConfig {
    
    /**
     * 首选字体名称
     */
    private String preferredFont = "SimSun";
    
    /**
     * 字体大小
     */
    private int fontSize = 50;
    
    /**
     * 是否启用字体缓存
     */
    private boolean enableCache = true;
    
    /**
     * 是否启用详细日志
     */
    private boolean enableDebugLog = false;
    
    /**
     * 自定义字体文件路径（可选）
     */
    private String customFontPath;
    
    /**
     * 字体回退策略
     * AUTO: 自动选择最佳字体
     * SYSTEM_ONLY: 仅使用系统字体
     * RESOURCE_ONLY: 仅使用资源字体
     * CUSTOM_ONLY: 仅使用自定义字体
     */
    private FontFallbackStrategy fallbackStrategy = FontFallbackStrategy.AUTO;
    
    public enum FontFallbackStrategy {
        AUTO,
        SYSTEM_ONLY,
        RESOURCE_ONLY,
        CUSTOM_ONLY
    }
}
