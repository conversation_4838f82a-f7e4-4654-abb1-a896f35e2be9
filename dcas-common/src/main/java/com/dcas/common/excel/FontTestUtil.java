package com.dcas.common.excel;

import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.List;

/**
 * 字体测试工具类
 * 用于诊断和测试系统中可用的中文字体
 * 
 * <AUTHOR>
 * @date 2025/7/2
 * @since 1.0.0
 */
@Slf4j
public class FontTestUtil {
    
    private static final String TEST_TEXT = "测试中文字体显示效果";
    
    /**
     * 获取系统中所有可用的字体
     */
    public static String[] getAllAvailableFonts() {
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        return ge.getAvailableFontFamilyNames();
    }
    
    /**
     * 获取支持中文的字体列表
     */
    public static List<String> getChineseSupportedFonts() {
        List<String> chineseFonts = new ArrayList<>();
        String[] allFonts = getAllAvailableFonts();
        
        for (String fontName : allFonts) {
            Font font = new Font(fontName, Font.PLAIN, 12);
            if (canDisplayChinese(font)) {
                chineseFonts.add(fontName);
            }
        }
        
        return chineseFonts;
    }
    
    /**
     * 测试字体是否支持中文
     */
    public static boolean canDisplayChinese(Font font) {
        for (char c : TEST_TEXT.toCharArray()) {
            if (!font.canDisplay(c)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 打印系统字体信息
     */
    public static void printSystemFontInfo() {
        log.info("=== 系统字体信息 ===");
        
        String[] allFonts = getAllAvailableFonts();
        log.info("系统总字体数量: {}", allFonts.length);
        
        List<String> chineseFonts = getChineseSupportedFonts();
        log.info("支持中文的字体数量: {}", chineseFonts.size());
        
        if (!chineseFonts.isEmpty()) {
            log.info("支持中文的字体列表:");
            for (String fontName : chineseFonts) {
                log.info("  - {}", fontName);
            }
        } else {
            log.warn("未找到支持中文的字体！");
        }
        
        // 测试默认字体
        Font defaultFont = new Font(Font.SANS_SERIF, Font.PLAIN, 12);
        log.info("默认字体: {}, 支持中文: {}", 
                defaultFont.getFontName(), canDisplayChinese(defaultFont));
    }
    
    /**
     * 测试特定字体的中文显示能力
     */
    public static void testFontChineseSupport(String fontName) {
        log.info("=== 测试字体: {} ===", fontName);
        
        Font font = new Font(fontName, Font.PLAIN, 12);
        log.info("实际字体名称: {}", font.getFontName());
        log.info("字体族: {}", font.getFamily());
        log.info("字体样式: {}", font.getStyle());
        log.info("字体大小: {}", font.getSize());
        
        boolean supportsChinese = canDisplayChinese(font);
        log.info("支持中文: {}", supportsChinese);
        
        if (!supportsChinese) {
            log.info("不支持的字符:");
            for (char c : TEST_TEXT.toCharArray()) {
                if (!font.canDisplay(c)) {
                    log.info("  - '{}' (Unicode: {})", c, (int) c);
                }
            }
        }
    }
    
    /**
     * 生成字体测试图片（用于验证字体渲染效果）
     */
    public static BufferedImage generateFontTestImage(String fontName, int fontSize) {
        Font font = new Font(fontName, Font.PLAIN, fontSize);
        
        // 创建临时图像来计算文本尺寸
        BufferedImage tempImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
        Graphics2D tempG2d = tempImage.createGraphics();
        tempG2d.setFont(font);
        FontMetrics metrics = tempG2d.getFontMetrics();
        
        int textWidth = metrics.stringWidth(TEST_TEXT);
        int textHeight = metrics.getHeight();
        tempG2d.dispose();
        
        // 创建实际图像
        int imageWidth = textWidth + 40;
        int imageHeight = textHeight + 40;
        BufferedImage image = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_ARGB);
        
        Graphics2D g2d = image.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // 设置背景色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, imageWidth, imageHeight);
        
        // 绘制文本
        g2d.setColor(Color.BLACK);
        g2d.setFont(font);
        g2d.drawString(TEST_TEXT, 20, 20 + metrics.getAscent());
        
        g2d.dispose();
        return image;
    }
    
    /**
     * 主方法，用于命令行测试
     */
    public static void main(String[] args) {
        printSystemFontInfo();
        
        if (args.length > 0) {
            for (String fontName : args) {
                testFontChineseSupport(fontName);
            }
        }
    }
}
