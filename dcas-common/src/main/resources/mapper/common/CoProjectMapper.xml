<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.CoProjectMapper">

    <sql id="Base_Column_List">
        project_id
        as projectId,
        bill_no as billNo,
        project_name as projectName,
        project_type as projectType,
        service_type as serviceType,
        signed_date as signedDate,
        project_manager as projectManager,
        contract_content as contractContent,
        customer_id as customerId,
        customer_name as customerName,
        customer_director as customerDirector,
        duration,
        begin_date as beginDate,
        plan_date  as planDate,
        completed_date as completedDate,
        status
    </sql>

    <select id="queryProjectList" parameterType="com.dcas.common.model.dto.QueryProjectDTO" resultType="com.dcas.common.model.vo.QueryProjectVo">
        select
        <include refid="Base_Column_List"/>
        from co_project
        <where>
            del_flag = '0' and user_id = #{userId}
            <!--在点击项目名称、点编辑时用到的单行查询-->
            <if test="dto.projectId != null and dto.projectId !='' ">
                and project_id = #{dto.projectId}
            </if>
            <if test="dto.projectName != null and dto.projectName !='' ">
                and project_name like concat('%',#{dto.projectName},'%')
            </if>
            <if test="dto.projectType != null and dto.projectType !='' ">
                and project_type = #{dto.projectType}
            </if>
            <if test="dto.projectManager != null and dto.projectManager !='' ">
                and project_manager = #{dto.projectManager}
            </if>
            <if test="dto.status != null  and dto.status != '' or dto.status == 0">
                and status = #{dto.status}
            </if>
            <if test="dto.serviceType != null and dto.serviceType != '' ">
                and service_type = #{dto.serviceType}
            </if>
        </where>
        order by create_time DESC
    </select>

    <update id="updateProjectByIds" parameterType="java.util.List">
        update co_project set del_flag = '2' where project_id in
        <foreach collection="list" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </update>

    <select id="queryProjectRelatedCustomer" resultType="CoCustomer">
        select
        b.customer_id as customerId,
        b.customer_director as customerDirector,
        b.related_industry as relatedIndustry,
        b.related_district as relatedDistrict,
        b.related_overseas as relatedOverseas
        from co_project a join co_customer b on a.customer_id =b.customer_id
        <where>
            del_flag = '0'
            <if test="projectId != null and projectId !='' ">
                and project_id = #{projectId}
            </if>
        </where>
        order by project_id
    </select>
    <select id="queryProjectByOperationId" resultType="com.dcas.common.domain.entity.CoProject">
        select
            *
        from
            co_project p inner join co_operation o on p.project_id = o.project_id
        where
            o.operation_id = #{operationId}
    </select>
    <select id="queryProjectByUserAndParams" parameterType="com.dcas.common.model.dto.QueryProjectDTO" resultType="com.dcas.common.domain.entity.CoProject">
        SELECT distinct cp.*
        FROM co_project cp
        WHERE cp.del_flag = '0'
        <if test="projectName != null and projectName != '' ">
            AND cp.project_name LIKE concat('%', #{projectName}, '%')
        </if>
        <if test="projectType != null and projectType != '' ">
            AND cp.project_type = #{projectType}
        </if>
        <if test="projectManager != null and projectManager != '' ">
            AND cp.project_manager LIKE concat('%', #{projectManager}, '%')
        </if>
        <if test="serviceType != null and serviceType != '' ">
            AND cp.service_type = #{serviceType}
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by cp.create_time desc
    </select>
    <select id="userWorks" parameterType="com.dcas.common.model.dto.QueryOperationOnGoingViewDto" resultType="com.dcas.common.model.vo.ProjectWorkVO">
        select *
        from (
                 select co.operation_id        id,
                        co.operation_name   as name,
                        cp.project_name,
                        cp.project_manager,
                        cp.customer_id,
                        co.related_industry as industry,
                        co.related_district as region,
                        1                   as type,
                        co.create_by,
                        co.create_time,
                        co.progress,
                        co.status,
                        co.user_id,
                        null                as userAccount,
                        co.executor,
                        co.executor_account as executorAccount,
                        co.reviewer,
                        co.reviewer_account as reviewerAccount,
                        co.service_content,
                        co.dept_id
                 from co_operation co
                          inner join co_project cp on co.project_id = cp.project_id
                 where co.del_flag = '0'
                 union all
                 select so.id::varchar,
                        so.name,
                        cp.project_name,
                        cp.project_manager,
                        cp.customer_id,
                        so.industry,
                        so.region,
                        2            as type,
                        so.create_by,
                        so.create_time,
                        so.progress,
                        so.status,
                        so.user_id   as userId,
                        so.create_by as userAccount,
                        so.executor,
                        so.executor_account as executorAccount,
                        null         as reviewer,
                        null         as reviewerAccount,
                        null         as serviceContent,
                        so.dept_id
                 from security_operation so
                          left join co_project cp on so.project_id = cp.project_id
                 where so.del_flag = '0'
             ) t where 1=1
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by create_time desc
    </select>
    <select id="projectWorks" resultType="com.dcas.common.model.vo.ProjectWorkVO">
        select co.operation_id        id,
               co.operation_name   as name,
               cp.project_id,
               cp.project_name,
               cp.project_manager,
               cp.customer_id,
               co.related_industry as industry,
               co.related_district as region,
               1                   as type,
               co.create_by,
               co.create_time,
               co.progress,
               co.status,
               co.user_id,
               null                as userAccount,
               co.executor,
               co.executor_account as executorAccount,
               co.reviewer,
               co.reviewer_account as reviewerAccount,
               co.service_content
        from co_project cp left join co_operation co on co.project_id = cp.project_id and co.del_flag = '0'
        where cp.del_flag = '0'
        <if test="projectId != null and projectId != '' ">
            and cp.project_id = #{projectId}
        </if>
        union all
        select so.id::varchar,
               so.name,
               cp.project_id,
               cp.project_name,
               cp.project_manager,
               cp.customer_id,
               so.industry,
               so.region,
               2            as type,
               so.create_by,
               so.create_time,
               so.progress,
               so.status,
               null         as userId,
               so.create_by as userAccount,
               so.executor,
               so.executor_account,
               null as reviewer,
               null as reviewer_account,
               null as serviceContent
        from co_project cp left join security_operation so on so.project_id = cp.project_id
        where cp.del_flag = '0'
        <if test="projectId != null and projectId != '' ">
            and cp.project_id = #{projectId}
        </if>
        order by create_time desc
    </select>
    <select id="projectWorksWithCostomerId" resultType="com.dcas.common.model.vo.ProjectWorkVO">
        select * from (
        select co.operation_id id,
        co.operation_name as name,
        cp.project_id,
        cp.project_name,
        cp.project_manager,
        cp.customer_id,
        co.related_industry as industry,
        co.related_district as region,
        1 as type,
        co.create_by,
        co.create_time,
        co.progress,
        co.status,
        co.user_id,
        co.dept_id,
        null as userAccount,
        co.executor_account as executor,
        co.executor_account,
        co.reviewer_account as reviewer,
        co.reviewer_account,
        co.service_content
        from co_project cp left join co_operation co on co.project_id = cp.project_id and co.del_flag = '0'
        where cp.del_flag = '0'
        <if test="customerId != null and customerId != '' ">
            and cp.customer_id = #{customerId}
        </if>
        union all
        select so.id::varchar,
        so.name,
        cp.project_id,
        cp.project_name,
        cp.project_manager,
        cp.customer_id,
        so.industry,
        so.region,
        2 as type,
        so.create_by,
        so.create_time,
        so.progress,
        so.status,
        so.user_id,
        so.dept_id,
        so.create_by as userAccount,
        so.executor,
        so.executor_account,
        null as reviewer,
        null as reviewer_account,
        null as serviceContent
        from co_project cp left join security_operation so on so.project_id = cp.project_id
        where cp.del_flag = '0'
        <if test="customerId != null and customerId != '' ">
            and cp.customer_id = #{customerId}
        </if>
        ) t where 1=1
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by create_time desc
    </select>
    <select id="queryProjectIdsByCustomerId" resultType="java.lang.String">
        select project_id from co_project where del_flag = '0' and customer_id = #{customerId}
    </select>

</mapper>
