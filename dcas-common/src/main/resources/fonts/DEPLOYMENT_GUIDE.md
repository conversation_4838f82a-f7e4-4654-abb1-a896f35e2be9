# 中文水印字体部署指南

## 问题描述
在CentOS服务器上导出Excel时，中文水印显示为"口"字符，这是由于服务器缺少中文字体导致的。

## 解决方案

### 方案一：安装系统中文字体（推荐）

#### 1. 使用脚本自动安装
```bash
# 上传并执行字体安装脚本
chmod +x install-chinese-fonts.sh
sudo ./install-chinese-fonts.sh
```

#### 2. 手动安装字体包
```bash
# CentOS/RHEL 7/8
sudo yum install -y fontconfig
sudo yum install -y google-noto-sans-cjk-fonts google-noto-serif-cjk-fonts
sudo yum install -y wqy-microhei-fonts wqy-zenhei-fonts

# 刷新字体缓存
sudo fc-cache -fv

# 验证字体安装
fc-list :lang=zh
```

#### 3. Ubuntu/Debian系统
```bash
sudo apt-get update
sudo apt-get install -y fontconfig
sudo apt-get install -y fonts-noto-cjk fonts-wqy-microhei fonts-wqy-zenhei

# 刷新字体缓存
sudo fc-cache -fv
```

### 方案二：使用内置字体资源

#### 1. 获取字体文件
从Windows系统复制字体文件：
- 路径：`C:\Windows\Fonts\`
- 推荐文件：`simsun.ttc`（宋体）、`simhei.ttf`（黑体）

#### 2. 放置字体文件
将字体文件放置到项目资源目录：
```
dcas-common/src/main/resources/fonts/
├── SimSun.ttf
├── simhei.ttf
└── NotoSansCJKsc-Regular.otf
```

#### 3. 重新打包部署
```bash
mvn clean package
```

### 方案三：配置自定义字体路径

在`application.yml`中添加配置：
```yaml
dcas:
  watermark:
    font:
      preferred-font: "SimSun"
      font-size: 50
      enable-cache: true
      enable-debug-log: true
      custom-font-path: "/opt/fonts/SimSun.ttf"
      fallback-strategy: AUTO
```

## 验证方法

### 1. 检查系统字体
```bash
# 查看所有中文字体
fc-list :lang=zh

# 查看特定字体
fc-list | grep -i "sim\|noto\|wqy"
```

### 2. 应用程序日志
启动应用后查看日志，应该看到类似信息：
```
INFO  - 系统可用字体数量: 156
INFO  - 使用系统字体: SimSun 来显示中文水印
INFO  - 水印使用字体: SimSun, 大小: 50, 是否支持中文: true
```

### 3. 测试水印导出
导出包含中文水印的Excel文件，检查中文是否正常显示。

## 故障排除

### 问题1：字体安装后仍显示为"口"
**解决方法：**
1. 重启Java应用程序
2. 检查字体缓存：`sudo fc-cache -fv`
3. 验证字体安装：`fc-list :lang=zh`

### 问题2：找不到合适的字体
**解决方法：**
1. 启用调试日志：`dcas.watermark.font.enable-debug-log: true`
2. 查看应用日志中的字体检测信息
3. 手动指定字体路径

### 问题3：性能问题
**解决方法：**
1. 启用字体缓存：`dcas.watermark.font.enable-cache: true`
2. 使用系统字体而非资源字体
3. 指定首选字体避免遍历

## 最佳实践

1. **生产环境推荐**：使用方案一安装系统字体
2. **开发环境**：可以使用方案二的内置字体资源
3. **容器化部署**：在Dockerfile中安装字体包
4. **监控**：定期检查字体相关的错误日志

## Docker部署示例

```dockerfile
FROM openjdk:8-jre-alpine

# 安装字体包
RUN apk add --no-cache fontconfig ttf-dejavu

# 或者复制字体文件
COPY fonts/*.ttf /usr/share/fonts/truetype/
RUN fc-cache -f

# 应用程序
COPY app.jar /app.jar
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 联系支持
如果问题仍然存在，请提供以下信息：
1. 操作系统版本
2. Java版本
3. 应用程序日志
4. `fc-list :lang=zh` 命令输出
