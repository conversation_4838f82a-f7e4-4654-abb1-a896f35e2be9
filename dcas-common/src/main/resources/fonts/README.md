# 字体资源目录

此目录用于存放中文字体文件，以解决在没有中文字体的Linux服务器上水印显示为"口"的问题。

## 支持的字体格式
- .ttf (TrueType Font)
- .otf (OpenType Font)

## 推荐的字体文件
1. **SimSun.ttf** - 宋体字体文件
2. **NotoSansCJKsc-Regular.otf** - Google Noto中文字体
3. **SourceHanSansCN-Regular.otf** - 思源黑体中文字体

## 字体文件获取方式

### 1. 从Windows系统复制
可以从Windows系统的 `C:\Windows\Fonts\` 目录下复制以下字体文件：
- simsun.ttc (宋体)
- simhei.ttf (黑体)
- msyh.ttc (微软雅黑)

### 2. 下载开源字体
- **Noto Sans CJK**: https://github.com/googlefonts/noto-cjk
- **Source Han Sans**: https://github.com/adobe-fonts/source-han-sans

### 3. 安装字体到系统
在CentOS/RHEL系统上安装中文字体：
```bash
# 安装基础中文字体包
yum install -y fontconfig
yum install -y dejavu-fonts-common dejavu-sans-fonts dejavu-serif-fonts
yum install -y google-noto-sans-cjk-fonts
yum install -y google-noto-serif-cjk-fonts

# 或者安装文泉驿字体
yum install -y wqy-microhei-fonts wqy-zenhei-fonts

# 刷新字体缓存
fc-cache -fv
```

## 使用说明
1. 将字体文件放置在此目录下
2. 重新打包应用程序
3. 水印工具会自动检测并使用这些字体文件

## 注意事项
- 字体文件可能受版权保护，请确保合法使用
- 建议使用开源字体以避免版权问题
- 字体文件会增加JAR包大小，请根据需要选择合适的字体
