#!/bin/bash

# 中文字体安装脚本
# 适用于CentOS/RHEL系统

echo "开始安装中文字体..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

# 更新系统包
echo "更新系统包..."
yum update -y

# 安装fontconfig
echo "安装fontconfig..."
yum install -y fontconfig

# 安装基础字体
echo "安装基础字体..."
yum install -y dejavu-fonts-common dejavu-sans-fonts dejavu-serif-fonts

# 安装Google Noto中文字体
echo "安装Google Noto中文字体..."
yum install -y google-noto-sans-cjk-fonts google-noto-serif-cjk-fonts

# 安装文泉驿字体
echo "安装文泉驿字体..."
yum install -y wqy-microhei-fonts wqy-zenhei-fonts

# 创建字体目录
echo "创建字体目录..."
mkdir -p /usr/share/fonts/chinese

# 刷新字体缓存
echo "刷新字体缓存..."
fc-cache -fv

# 检查安装的中文字体
echo "检查已安装的中文字体..."
fc-list :lang=zh

echo "中文字体安装完成！"
echo "建议重启Java应用程序以使字体生效。"
