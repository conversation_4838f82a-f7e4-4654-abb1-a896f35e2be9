# 水印字体配置示例
# 将此配置添加到主配置文件 application.yml 中

dcas:
  watermark:
    font:
      # 首选字体名称（如果系统中存在此字体，将优先使用）
      preferred-font: "SimSun"
      
      # 字体大小
      font-size: 50
      
      # 是否启用字体缓存（提高性能）
      enable-cache: true
      
      # 是否启用详细日志（用于调试字体问题）
      enable-debug-log: false
      
      # 自定义字体文件路径（可选，如果指定则优先使用此字体文件）
      # custom-font-path: "/opt/fonts/SimSun.ttf"
      
      # 字体回退策略
      # AUTO: 自动选择最佳字体（推荐）
      # SYSTEM_ONLY: 仅使用系统字体
      # RESOURCE_ONLY: 仅使用资源字体
      # CUSTOM_ONLY: 仅使用自定义字体
      fallback-strategy: AUTO

# 生产环境推荐配置
---
spring:
  profiles: prod
  
dcas:
  watermark:
    font:
      preferred-font: "SimSun"
      font-size: 50
      enable-cache: true
      enable-debug-log: false
      fallback-strategy: SYSTEM_ONLY

# 开发环境配置（启用调试日志）
---
spring:
  profiles: dev
  
dcas:
  watermark:
    font:
      preferred-font: "SimSun"
      font-size: 50
      enable-cache: true
      enable-debug-log: true
      fallback-strategy: AUTO

# 测试环境配置
---
spring:
  profiles: test
  
dcas:
  watermark:
    font:
      preferred-font: "SansSerif"
      font-size: 40
      enable-cache: false
      enable-debug-log: true
      fallback-strategy: AUTO
