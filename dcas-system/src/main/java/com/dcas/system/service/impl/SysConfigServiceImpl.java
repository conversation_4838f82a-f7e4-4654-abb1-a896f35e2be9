package com.dcas.system.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.dcas.common.constant.CacheConstants;
import com.dcas.common.constant.UserConstants;
import com.dcas.common.core.redis.RedisCache;
import com.dcas.common.core.text.Convert;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.StringUtils;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.mapper.SysConfigMapper;
import com.dcas.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 参数配置 服务层实现
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SysConfigServiceImpl implements ISysConfigService {
    private final RedisCache redisCache;
    private final SysConfigMapper configMapper;

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    public SysConfig selectConfigById(Long configId) {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        return configMapper.selectConfig(config);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue)) {
            return configValue;
        }
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = configMapper.selectConfig(config);
        if (StringUtils.isNotNull(retConfig)) {
            redisCache.setCacheObject(getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取验证码开关
     *
     * @return true开启，false关闭
     */
    @Override
    public boolean selectCaptchaEnabled() {
        String captchaEnabled = selectConfigByKey("sys.account.captchaEnabled");
        if (StringUtils.isEmpty(captchaEnabled)) {
            return true;
        }
        return Convert.toBool(captchaEnabled);
    }

    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfig> selectConfigList(SysConfig config) {
        return configMapper.selectConfigList(config);
    }

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int insertConfig(SysConfig config) {
        int row = configMapper.insertConfig(config);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int updateConfig(SysConfig config) {
        int row = configMapper.updateConfig(config);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(Long[] configIds) {
        for (Long configId : configIds) {
            SysConfig config = selectConfigById(configId);
            if (StringUtils.equals(UserConstants.YES, config.getConfigType())) {
                throw new ServiceException(String.format("内置参数【%1$s】不能删除 " , config.getConfigKey()));
            }
            configMapper.deleteConfigById(configId);
            redisCache.deleteObject(getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache() {
        List<SysConfig> configsList = configMapper.selectConfigList(new SysConfig());
        for (SysConfig config : configsList) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache() {
        Collection<String> keys = redisCache.keys(CacheConstants.SYS_CONFIG_KEY + "*");
        redisCache.deleteObject(keys);
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public String checkConfigKeyUnique(SysConfig config) {
        long configId = StringUtils.isNull(config.getConfigId()) ? -1L : config.getConfigId();
        SysConfig info = configMapper.checkConfigKeyUnique(config.getConfigKey());
        if (StringUtils.isNotNull(info) && info.getConfigId() != configId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return CacheConstants.SYS_CONFIG_KEY + configKey;
    }

    @Override
    public void saveOrUpdateSysEdition(String sysEdition, String dcasadmin) {
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigKey(SysConfigEnum.SYSTEM_EDITION.getKey());
        sysConfig.setConfigValue(sysEdition);
        sysConfig.setConfigName("系统版本类型");
        sysConfig.setConfigType(UserConstants.NO);
        sysConfig.setRemark("BE-基础版,SE-标准版,EE-企业版,UE-旗舰版");
        sysConfig.setCreateBy(dcasadmin != null ? dcasadmin :SecurityUtils.getUsername());
        sysConfig.setUpdateTime(new Date());
        sysConfig.setCreateTime(new Date());
        sysConfig.setUpdateBy(dcasadmin != null ? dcasadmin :SecurityUtils.getUsername());
        SysConfig old = configMapper.selectConfigByKey(SysConfigEnum.SYSTEM_EDITION.getKey());
        if (old == null){
            log.info("insert edition config : {}", sysEdition);
            insertConfig(sysConfig);
        } else {
            log.info("update edition config : {}", sysEdition);
            sysConfig.setConfigId(old.getConfigId());
            updateConfig(sysConfig);
        }
    }

    @Override
    public void saveOrUpdateJobNum(Integer jobNum, String user) {
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigKey(SysConfigEnum.JOB_NUM.getKey());
        sysConfig.setConfigValue(String.valueOf(jobNum));
        sysConfig.setConfigName("作业限制数量");
        sysConfig.setConfigType(UserConstants.NO);
        sysConfig.setRemark("根据license作业数量特性来定");
        sysConfig.setCreateBy(user != null ? user :SecurityUtils.getUsername());
        sysConfig.setUpdateTime(new Date());
        sysConfig.setCreateTime(new Date());
        sysConfig.setUpdateBy(user != null ? user :SecurityUtils.getUsername());
        SysConfig old = configMapper.selectConfigByKey(SysConfigEnum.JOB_NUM.getKey());
        if (old == null){
            log.info("insert jobNum config : {}", jobNum);
            insertConfig(sysConfig);
        } else {
            log.info("update jobNum config : {}", jobNum);
            sysConfig.setConfigId(old.getConfigId());
            sysConfig.setConfigValue(String.valueOf(Integer.parseInt(old.getConfigValue()) + jobNum));
            updateConfig(sysConfig);
        }
    }

    @Override
    public int count(String key) {
        return configMapper.count(key);
    }

    @Override
    public void resetCache() {
        clearCache();
        loadingConfigCache();
    }

    private void clearCache() {
        Collection<String> keys = redisCache.keys(CacheConstants.ITEM_FILE_KEY + "*");
        redisCache.deleteObject(keys);
    }
}
