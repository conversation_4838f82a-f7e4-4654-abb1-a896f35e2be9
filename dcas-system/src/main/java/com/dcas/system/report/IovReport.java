package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.*;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.excel.SpecPersonRiskExcel;
import com.dcas.common.model.vo.*;
import com.dcas.common.utils.DcasUtil;
import com.dcas.common.utils.StringUtils;
import com.dcas.system.report.attachment.AttachmentReportFactory;
import com.dcas.system.service.CoVerificationService;
import com.dcas.system.service.IRiskAnalysisService;
import com.dcas.system.service.ScanTaskService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className IovReport
 * @description 车联网报告
 * @date 2024/09/13 14:17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IovReport extends AbstractReport implements ReportInterface {

    private final ModelFileMapper modelFileMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final CoLegalMapper coLegalMapper;
    private final FormConfigMapper formConfigMapper;
    private final CoConstantMapper coConstantMapper;
    private final IndicatorResultMapper indicatorResultMapper;
    private final CoVerificationService coVerificationService;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final StandardItemMapper standardItemMapper;
    private final CoGapAnalysisMapper coGapAnalysisMapper;
    private final AdviseRiskMapper adviseRiskMapper;
    private final IRiskAnalysisService iRiskAnalysisService;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;

    @Value("${safety.profile}")
    private String basePath;

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
                try {
                    dto.setNeedExcludeContent(getNeedExcludeContent());
                    return process(dto, poVo, modelId);
                } catch (IOException e) {
                    log.error("导出报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }), CompletableFuture.supplyAsync(() -> {
                try {
                    return AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.IOV).exportWord( dto,
                        poVo);
                } catch (Exception e) {
                    log.error("导出资产清单附件失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }), CompletableFuture.supplyAsync(() -> {
                try {
                    return  ReportFactory.getReportHandler(ReportTypeEnum.TEC_DETECTION).exportWord(dto, poVo);
                } catch (Exception e) {
                    log.error("导出技术检测报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            })).thenRun(() -> addScanReportToPath(filePathList, dto.getOperationId()))
            .thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
                try {
                    dto.setNeedExcludeContent(getNeedExcludeContent());
                    return process(dto, poVo, null);
                } catch (IOException e) {
                    log.error("导出报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }), CompletableFuture.supplyAsync(() -> {
                try {
                    return AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.IOV).exportWord( dto,
                        poVo);
                } catch (Exception e) {
                    log.error("导出资产清单附件失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }), CompletableFuture.supplyAsync(() -> {
                try {
                    return  ReportFactory.getReportHandler(ReportTypeEnum.TEC_DETECTION).exportWord(dto, poVo);
                } catch (Exception e) {
                    log.error("导出技术检测报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            })).thenRun(() -> addScanReportToPath(filePathList, dto.getOperationId()))
            .thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @Override
    @SchemaSwitch(ExportWordDto.class)
    public String process(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws IOException {

        //获取模板地址，注意k8s无法识别中文文件名，中文文件失效
        InputStream inputStream;
        ModelFile modelFile = modelFileMapper.selectOne(new QueryWrapper<ModelFile>().eq("model_id", modelId));
        if (modelFile == null) {
            //获取模板地址，注意k8s无法识别中文文件名，中文文件失效
            ClassPathResource classPathResource = new ClassPathResource("template/DCASReportExportTemplate-iov.docx");
            inputStream = classPathResource.getInputStream();
        } else {
            inputStream = new FileInputStream(modelFile.getFilePath());
        }
        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();
        List<ExportWordChart> analysisChartList =
            chartList.stream().filter(s -> s.getName().contains("图") || s.getName().contains("表格"))
                .collect(Collectors.toList());

        // 报告公共部分
        putCommonInfo(poVo, model);

        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getOperationId());
        queryWrapper.orderByDesc("bus_system");
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
        List<String> coInventories =
            coInventoryList.stream().map(CoInventory::getBusSystem).distinct().collect(Collectors.toList());

        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(operationId);
        requestModel.setPrivator(operationIdDto);
        int titleIndex = 2;

        // 图片赋值
        putModelPicture(chartList, model);

        //评估内容
        List<Long> serviceContentList = JSON.parseArray("[" + poVo.getServiceContent() + "]", Long.class);

        //3.资产梳理
        //3.2.2 高危数据权限分析
        model.put("assetAnalysis", false);
        if (serviceContentList.contains(LabelEnum.ZCSL.getCode())) {
            model.put("assetAnalysis", true);
            titleIndex++;
            int titleThree = titleIndex;
            model.put("titleThree", titleThree);
            assetAnalysis(chartList, requestModel, model, coInventories, coInventoryList, serviceContentList);
        }

        //4.安全现状
        //4.1.1	评估概述
        model.put("currentSituationAnalysis", false);
        if (serviceContentList.contains(LabelEnum.AQXZ.getCode())) {
            model.put("currentSituationAnalysis", true);
            titleIndex++;
            int titleFour = titleIndex;
            model.put("titleFour", titleFour);
            currentSituationAnalysis(operationId, model, analysisChartList);
        }

        //5.安全评估
        model.put("riskAssessment", false);
        if (serviceContentList.contains(LabelEnum.FXPG.getCode())) {
            model.put("riskAssessment", true);
            titleIndex++;
            int titleFive = titleIndex;
            model.put("titleFive", titleFive);
            riskAssessment(model, dto, coInventoryList, operationId, chartList, serviceContentList);
        }

        // 6 处置建议
        // 6.1合规风险
        model.put("processSuggestions", false);
        if (serviceContentList.contains(LabelEnum.CZJY.getCode())) {
            model.put("processSuggestions", true);
            titleIndex++;
            int titleSix = titleIndex;
            model.put("titleSix", titleSix);
            processSuggestions(operationId, model);
        }

        // 附件 资产列表
        if (model.containsKey("assetsCheck") && (Boolean)model.get("assetsCheck")) {
            AtomicInteger assetSort = new AtomicInteger(0);
            List<ReportAssetVO> assetList = coInventoryList.stream().map(
                co -> ReportAssetVO.builder().sort(assetSort.incrementAndGet()).schemaName(co.getSchemaName())
                    .dataAsset(co.getDataAsset()).assetComment(co.getAssetComment()).busSystem(co.getBusSystem())
                    .sensitiveLevel(co.getSensitiveLevel()).build()).collect(Collectors.toList());
            model.put("assetList", assetList);
        }

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config = Configure.builder().bind("userAssetAuthorityList", policy).bind("tabList", policy)
            .bind("loopholeByIPSystemList", policy).bind("legalList", policy).bind("assetList", policy)
            .bind("assetRiskHighLevelList", policy).bind("assetRiskMediumLevelList", policy)
            .bind("abilityAdviseContents", policy).bind("legalAdviseContents", policy)
            .bind("legalAdviseContents", policy).bind("detectionRecordList", policy)
            .bind("detectionPointDetails", policy).bind("tocContents", tocPolicy).bind("dataSaftyHighSuggests", policy)
            .bind("dataTecHighSuggests", policy).bind("dataSaftyMidSuggests", policy).bind("dataTecMidSuggests", policy)
            .bind("scanRiskList", policy).bind("busSystemAuthorityList", policy)
            .bind("riskList", policy).bind("commonList", policy)
            .bind("riskCalcList",policy)
            .bind("legalRiskList", policy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s报告.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = new FileOutputStream(path);
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;

    }

    private void riskAssessment(Map<String, Object> model, ExportWordDto privator, List<CoInventory> coInventoryList,
        String operationId, List<ExportWordChart> chartList, List<Long> serviceContentList) {

            List<Map<String, Object>> fileRefList = Lists.newArrayList();
            AtomicInteger fileIndex = new AtomicInteger(1);

            // 5.1基础评估
            baseAssessment(model, chartList, serviceContentList, privator);

            // 5.2合规评估
            lawAssessment(model, privator, serviceContentList, operationId, fileRefList, fileIndex);

            // 5.3风险评估
            riskContentsAssessment(operationId, model, serviceContentList,  fileRefList, fileIndex, chartList, privator, coInventoryList);
    }


    private void riskContentsAssessment(String operationId, Map<String, Object> model, List<Long> serviceContentList,
        List<Map<String, Object>> fileRefList, AtomicInteger fileIndex, List<ExportWordChart> chartList,
        ExportWordDto privator, List<CoInventory> coInventoryList) {
        // 手动push知识库版本
        pushVersion(operationId);
        List<String> templateFileList = coVerificationMapper.selectTemplateFileByOperationId(operationId);
        if (!CollectionUtils.isEmpty(templateFileList)) {
            templateFileList.forEach(s -> {
                Map<String, Object> fileMap = new HashMap<>(16);
                fileMap.put("sort", fileIndex.getAndIncrement());
                fileMap.put("name", s);
                fileRefList.add(fileMap);
            });
        }
        model.put("fileRefList", fileRefList);
        model.put("riskContentsAssessment", false);
        if (serviceContentList.contains(LabelEnum.SMZQ.getCode()) && !privator.getNeedExcludeContent()
            .contains(LabelEnum.SMZQ)) {
            model.put("riskContentsAssessment", true);

            // 5.3.2 定性风险分析
            List<Map<String, Object>> riskList = new ArrayList<>();
            List<AdviseRiskDTO> adviseRisks = adviseRiskMapper.selectRiskContent(operationId);
            AtomicInteger sort = new AtomicInteger(1);
            adviseRisks.stream().filter(adviseRiskDTO -> adviseRiskDTO.getRiskLevel() != null)
                .sorted(Comparator.comparingInt(AdviseRiskDTO::getRiskLevel).reversed()).forEach(adviseRiskDTO -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("sort", sort.getAndIncrement());
                    map.put("content", adviseRiskDTO.getContent());
                    map.put("riskLevel", adviseRiskDTO.getRiskLevel());
                    map.put("describe", adviseRiskDTO.getDescribe());
                    riskList.add(map);
                });
            model.put("riskList", riskList);

            // 5.3.3定量风险分析
            List<Map<String, Object>> riskCalcList = new ArrayList<>();
            List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
                .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

            String titlesStr = "";
            List<Map> resultList = new ArrayList<>();
            for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
                titlesStr = coModelAnalysisResult.getTitles();
                List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
                resultList.addAll(mapList);
            }
            List<FormConfigTreeVO> titles =
                JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
            Map<String, String> indexColumnMap = new HashMap<>(16);
            titles.forEach(formConfigTreeVO -> {
                if (formConfigTreeVO.getDataColumn()) {
                    String title = formConfigTreeVO.getTitle().trim();
                    switch (title) {
                        case "数据注释":
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "comment");
                            break;
                        case "数据级别":
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "dataLevel");
                            break;
                        case "脆弱性等级":
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "valLevel");
                            break;
                        case "威胁等级":
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "threatLevel");
                            break;
                        case "风险等级":
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskLevel");
                            break;
                        default:
                            break;
                    }
                } else {
                    if (CollUtil.isNotEmpty(formConfigTreeVO.getChildren())) {
                        formConfigTreeVO.getChildren().forEach(child -> {
                            if ("资产名称".equals(child.getTitle())) {
                                indexColumnMap.put(child.getDataIndex(), "name");
                            } else if ("敏感等级".equals(child.getTitle())) {
                                indexColumnMap.put(child.getDataIndex(), "sensitiveLevel");
                            }
                        });
                    } else {
                        if ("所属业务系统".equals(formConfigTreeVO.getTitle())) {
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystem");
                        }
                    }
                }
            });
            resultList.forEach(map -> {
                Map<String, Object> resultMap = new HashMap<>();
                for (Object o : map.keySet()) {
                    String key = (String)o;
                    if (indexColumnMap.get(key) == null) {
                        continue;
                    }
                    String column = indexColumnMap.get(key);
                    switch (column) {
                        case "busSystem":
                            resultMap.put("busSystem", (String)map.get(key));
                            break;
                        case "name":
                            resultMap.put("name", (String)map.get(key));
                            break;
                        case "sensitiveLevel":
                            resultMap.put("sensitiveLevel", (String)map.get(key));
                            break;
                        case "comment":
                            resultMap.put("comment", (String)map.get(key));
                            break;
                        case "dataLevel":
                            resultMap.put("dataLevel", (String)map.get(key));
                            break;
                        case "valLevel":
                            resultMap.put("valLevel", (String)map.get(key));
                            break;
                        case "threatLevel":
                            resultMap.put("threatLevel", (String)map.get(key));
                            break;
                        case "riskLevel":
                            resultMap.put("riskLevel", (String)map.get(key));
                            break;
                        default:
                            break;
                    }
                }
                riskCalcList.add(resultMap);
            });
            model.put("riskCalcList", riskCalcList.stream().limit(20).collect(Collectors.toList()));
        }

        //5.3.3.1	总体风险分析
        RiskAnalysisReportVO riskAnalysisReportVO = iRiskAnalysisService.queryRiskAnalysisReport(operationId, true);
        RiskAnalysisReportVO.AssetStatisticsChart assetStatisticsChart = riskAnalysisReportVO.getAssetStatisticsChart();
        int busSystemNum = riskAnalysisReportVO.getBusSystemRiskSortChart().getXAxisList().size();
        int riskTotal = riskAnalysisReportVO.getRiskTotal();
        // TODO 先固定指标 中风险 高风险 中高风险
        int mediumRiskAssetNum = 0;
        int highRiskAssetNum = 0;
        int moreThanMediumNum = 0;
        for (ReportConfigBaseDTO dto : assetStatisticsChart.getDataList()){
            if (dto.getConfigIndicator() == 519){
                mediumRiskAssetNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
            }
            if (dto.getConfigIndicator() == 520){
                highRiskAssetNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
            }
            if (dto.getConfigIndicator() == 521){
                moreThanMediumNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
            }
        }

        double highRiskAssetNumProportion = BigDecimal.valueOf((double)highRiskAssetNum)
            .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
            .multiply(BigDecimal.valueOf(100)).doubleValue();
        double mediumRiskAssetNumProportion = BigDecimal.valueOf((double)mediumRiskAssetNum)
            .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
            .multiply(BigDecimal.valueOf(100)).doubleValue();
        double moreThanMediumNumProportion = BigDecimal.valueOf((double)moreThanMediumNum)
            .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
            .multiply(BigDecimal.valueOf(100)).doubleValue();
        model.put("busSystemNum", busSystemNum);
        model.put("highRiskAssetNum", highRiskAssetNum);
        model.put("mediumRiskAssetNum", mediumRiskAssetNum);
        model.put("highRiskAssetNumProportion", highRiskAssetNumProportion);
        model.put("mediumRiskAssetNumProportion", mediumRiskAssetNumProportion);
        model.put("moreThanMediumNumProportion", moreThanMediumNumProportion);
        model.put("totalRiskLevel", covertRiskLevel(riskAnalysisReportVO.getComprehensiveChart().getValue()));

    }

    private Object covertRiskLevel(String value) {
        switch (value){
            case "1":
                return "低风险";
            case "2":
                return "中风险";
            case "3":
                return "高风险";
            default:
                return value;
        }
    }

    /**
     * 合规风险分析--比例
     *
     * @param operationId request
     * @return * @return QueryViewLegalFactorVo
     * @Date 2022/9/20 17:11
     */
    private QueryViewLegalResultVo queryLegalProportion(String operationId) {
        /**
         * -------总对标文件--------------
         * 总对标(法律)文件：lawTotalNum = distinct lawName
         * 法律：article_code包含NL 统计 distinct lawName
         * 法规：article_code包含GL、DR、LL、LR、IR、RL 统计 distinct lawName
         * 标准：article_code包含 GS、NS、IS、LS、TS 统计 distinct lawName
         *--------总对标项----------------
         * 注：按item_num分组统计
         * 总对标项：itemTotalNum = group by item_num
         * 合格率：（“完全符合项”* 1+”部分符合项“*0.5）/（”总对标项“）*100%
         * -------完全符合项--------------
         * 注：按item_num分组统计
         * 完全符合项：aNum = 分组结果 = “完全符合”
         * 完全符合项占比 = aNum / itemTotalNum *100%
         * -------部分符合项--------------
         * 注：按item_num分组统计
         * -------不符合项---------
         * 注：按item_num分组统计
         * -------不涉及项（不适用）--------
         *
         */
        QueryLegalDTO retrieveLegal = new QueryLegalDTO();
        retrieveLegal.setOperationId(operationId);
        retrieveLegal.setLabelId(LabelEnum.HFHG.getCode());
        retrieveLegal.setLawName(LegalModelEnum.ZHMB.getInfo());
        return this.getLegalResultList(retrieveLegal);
    }

    private void lawAssessment(Map<String, Object> model, ExportWordDto privator, List<Long> serviceContentList,
        String operationId, List<Map<String, Object>> fileRefList, AtomicInteger fileIndex) {
        model.put("lawAssessment", false);
        if (serviceContentList.contains(LabelEnum.HFHG.getCode()) && !privator.getNeedExcludeContent()
            .contains(LabelEnum.HFHG)) {
            model.put("lawAssessment", true);

            QueryViewLegalResultVo legalResultVo = queryLegalProportion(operationId);
            model.put("lawTotalNum", legalResultVo.getLawTotalNum());
            model.put("lawDocNum", legalResultVo.getLawDocNum());
            model.put("ruleDocNum", legalResultVo.getRuleDocNum());
            model.put("standardDocNum", legalResultVo.getStandardDocNum());
            model.put("itemTotalNum", legalResultVo.getItemTotalNum());
            model.put("qualifiedProportion", legalResultVo.getQualifiedProportion());
            model.put("countANum", legalResultVo.getCountANum());
            model.put("countBNum", legalResultVo.getCountBNum());
            model.put("countCNum", legalResultVo.getCountCNum());
            model.put("countDNum", legalResultVo.getCountDNum());
            model.put("countAProportion", legalResultVo.getCountAProportion());
            model.put("countBProportion", legalResultVo.getCountBProportion());
            model.put("countCProportion", legalResultVo.getCountCProportion());
            model.put("countDProportion", legalResultVo.getCountDProportion());

            //5.2.3	具体评估结果 5.2.3.1	{{co_legal.law_name}}评估详情
            List<Map<String, Object>> lawEvaluation = Lists.newArrayList();
            QueryWrapper<CoLegal> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.eq("operation_id", privator.getOperationId());
            List<CoLegal> coLegalList = coLegalMapper.selectList(queryWrapper2);
            Collator collator = Collator.getInstance(Locale.CHINA);
            if (CollUtil.isNotEmpty(coLegalList)) {
                //                Map<String, String> descMap = getLawDescribe(operationId);
                Set<String> lawType = coLegalList.stream().map(CoLegal::getLawName).collect(Collectors.toSet());
                for (String law : lawType) {
                    AtomicInteger index = new AtomicInteger(1);
                    Map<String, Object> map = new HashMap<>(lawType.size());
                    Map<String, Object> fileMap = new HashMap<>(lawType.size());
                    map.put("lawName", law);
                    List<LawReportDTO> list = coLegalList.stream()
                        .filter(l -> Objects.equals(l.getLawName(), law) && !OptEnum.D.getInfo().equals(l.getResult()))
                        .map(
                            legal -> LawReportDTO.builder()
                                .itemNum(StrUtil.sub(legal.getItemContent(), 0, 10))
                                .itemContent(legal.getItemContent()).itemExplain(legal.getItemExplain())
                                .result(legal.getResult()).remark(legal.getDesc())
                                .build()).sorted(Comparator.comparing(
                            i -> {
                                int sort = 9999;
                                try {
                                    sort = NumberChineseFormatter.chineseToNumber(StrUtil.subBetween(i.getItemNum(), "第", "条"));
                                } catch (Exception e) {
                                    if (StrUtil.containsAny(i.getItemNum(), StrUtil.DOT)) {
                                        try {
                                            sort = Integer.parseInt(StrUtil.subBefore(i.getItemNum(), StrUtil.DOT, false));
                                        } catch (Exception ignore){
                                        }
                                    }
                                }
                                return sort;
                            }))
                        .peek(dt -> dt.setSort(index.getAndIncrement())).collect(Collectors.toList());
                    map.put("legalList", list);
                    fileMap.put("name", law);
                    fileMap.put("sort", fileIndex.getAndIncrement());
                    lawEvaluation.add(map);
                    fileRefList.add(fileMap);
                }
            }

            int countA = 0,countB = 0,countC = 0,total = 0;
            BigDecimal score = BigDecimal.ZERO;
            for (CoLegal coLegal : coLegalList){
                if (!coLegal.getLawName().contains("智能网联汽车数据安全评估指南")){
                    continue;
                }
                total++;
                if (OptEnum.D.getInfo().equals(coLegal.getResult())){
                    continue;
                }
                if (OptEnum.A.getInfo().equals(coLegal.getResult())){
                    countA++;
                } else if (OptEnum.C.getInfo().equals(coLegal.getResult())){
                    countC++;
                } else {
                    countB++;
                }
                score = score.add(coLegal.getScore());
            }
            model.put("countA", countA);
            model.put("countB", countB);
            model.put("countC", countC);
            model.put("score", score.multiply(BigDecimal.valueOf(100).divide(BigDecimal.valueOf(total),2, RoundingMode.HALF_UP),MathContext.UNLIMITED));
            model.put("result", judgeResult(score));
            model.put("lawEvaluation", lawEvaluation);
        }

        // 合规分析
        adviseRiskAnalysis(operationId, model);
    }

    private Object judgeResult(BigDecimal score) {
        if (score.compareTo(BigDecimal.valueOf(90)) >= 0) {
            return "优秀";
        } else if (score.compareTo(BigDecimal.valueOf(80)) >= 0 && score.compareTo(BigDecimal.valueOf(89)) >= 0) {
            return "良好";
        } else if (score.compareTo(BigDecimal.valueOf(70)) >= 0 && score.compareTo(BigDecimal.valueOf(79)) >= 0) {
            return "合格";
        } else {
            return "不合格";
        }
    }

    private List<ReportAssetRiskVO> queryAssetRisk(String operationId, String system, Map<String, String> commentMap,
        Long modelId) {
        // 手动push知识库版本
        pushVersion(operationId);
        QueryWrapper<IndicatorResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        queryWrapper.eq("label_id", LabelEnum.SMZQ.getCode());
        queryWrapper.eq("bus_system", system);
        List<IndicatorResult> indicatorResultList = indicatorResultMapper.selectList(queryWrapper);
        QueryWrapper<CoConstant> query = new QueryWrapper<>();
        query.eq("customer_id", coOperationMapper.selectCustomIdByOperationId(operationId));
        List<FormConfig> formConfigList =
            formConfigMapper.selectList(new QueryWrapper<FormConfig>().eq("model_id", modelId));
        CoConstant coConstant = coConstantMapper.selectOne(query);
        Map<String, List<FormConfig>> formConfigMap = formConfigList.stream()
            .filter(formConfig -> formConfig.getParentId() != -1 && formConfig.getColFormat() != null)
            .collect(Collectors.groupingBy(FormConfig::getColFormat));
        List<FormConfig> percentageFormConfigs = formConfigMap.get(ColFormatEnum.PERCENTAGE.getValue());
        List<FormConfig> numberFormConfigs = formConfigMap.get(ColFormatEnum.NUMBER.getValue());
        Map<Integer, String> parentMap = formConfigList.stream().filter(formConfig -> formConfig.getParentId() == -1)
            .collect(Collectors.toMap(FormConfig::getColId, FormConfig::getColName));
        return indicatorResultList.stream().map(p -> {
            Map<String, Object> stepResultMap = (Map<String, Object>)JSONUtil.toBean(p.getResult(), Map.class);
            List<Integer> riskGradeList = new ArrayList<>();
            Map<Double, String> factorMap = new HashMap<>(16);
            numberFormConfigs.forEach(formConfig -> riskGradeList
                .add(Integer.parseInt((String)stepResultMap.get(String.valueOf(formConfig.getColFormula())))));

            percentageFormConfigs.forEach(formConfig -> factorMap
                .put(Double.parseDouble((String)stepResultMap.get(String.valueOf(formConfig.getColFormula()))),
                    parentMap.get(formConfig.getParentId())));
            riskGradeList.sort(Comparator.reverseOrder());
            Double max = Collections.max(factorMap.keySet());
            final String riskFactor = factorMap.get(max);
            //风险等级汇总统计
            IntSummaryStatistics gradeStatistics =
                riskGradeList.stream().mapToInt(Number::intValue).summaryStatistics();
            //单资产风险值：单个作业的单个资产的从数据采集到通用阶段的最大值（风险等级）
            int singleRiskValue = gradeStatistics.getMax();
            //高资产风险数量：先统计单资产风险值在矩阵中的风险等级
            String riskLevel = DcasUtil.getMatrixRisk(singleRiskValue, coConstant.getHighestSensitiveLevel());
            return ReportAssetRiskVO.builder().assetName(p.getAssertsName())
                .assetComment(commentMap.get(p.getBusSystem() + p.getAssertsName())).risk(riskLevel)
                .riskFactor(riskFactor).build();
        }).collect(Collectors.toList());
    }

    private void currentSituationAnalysis(String operationId, Map<String, Object> model,
        List<ExportWordChart> analysisChartList) {
        //查询现状核验表获取标签页
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", operationId);
        List<CoVerification> coVerifications = coVerificationMapper.selectList(query);
        if (CollectionUtils.isEmpty(coVerifications)) {
            return;
        }
        List<StandardItemDTO> standardItemDTOS = standardItemMapper.queryClassifyItems(operationId);

        // 排序
        CoVerification coVerification1 = coVerifications.get(0);
        model.put("modelName", coVerification1.getModelName());
        model.put("level", StringUtils.isNotNull(coVerification1.getLevel()) ? coVerification1.getLevel().trim() :
            coVerification1.getLevel());
        List<QueryModelVo> modelList = coVerificationService.listModel(operationId);
        List<String> typeList = new ArrayList<>();
        int selectedModelId = 0;
        for (QueryModelVo queryModelVo : modelList) {
            if (queryModelVo.getSelected()) {
                selectedModelId = queryModelVo.getModelId();
                typeList.addAll(queryModelVo.getTypeList());
            }
        }
        String tabNames = "";
        if (selectedModelId == 104) {
            tabNames = StringUtils.join(coVerifications.stream().map(CoVerification::getProcess).distinct()
                .map(s -> StrUtil.subAfter(s, StrUtil.C_SPACE, true))
                .sorted(Comparator.comparing(s -> StrUtil.subBefore(s, StrUtil.C_SPACE, true))).toArray(), "、");
        } else {
            tabNames = StringUtils.join(coVerifications.stream().map(CoVerification::getType).distinct()
                .sorted(Comparator.comparing(typeList::indexOf)).toArray(), "、");
        }
        model.put("tabNames", tabNames);

        //4.1.2.1	评估结果
        List<Map<String, Object>> analysisContents = new ArrayList<>();
        List<ExportWordChart> exportWordCharts = coGapAnalysisMapper.queryRemarkByOperationId(operationId);
        Map<String, String> remarkMap = new HashMap<>();
        if (CollUtil.isNotEmpty(exportWordCharts)){
            remarkMap =
                exportWordCharts.stream().filter(exportWordChart -> StrUtil.isNotEmpty(exportWordChart.getRemark()))
                    .collect(Collectors.toMap(ExportWordChart::getName, ExportWordChart::getRemark, (k1, k2) -> k1));
        }
        for (ExportWordChart chart : analysisChartList) {
            if (chart.getName().startsWith("基础评估分析-")) {
                continue;
            }
            Map<String, Object> content = new HashMap<>();
            content.put("chartName", chart.getName());
            List<Map<String, Object>> chartImages = new ArrayList<>();
            Map<String, Object> chartMap = new HashMap<>();

            Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));

            chartMap.put("chartImage", getPictureStream(imageMap));
            chartMap.put("chartImageName", chart.getName());
            chartMap.put("chartImageEvaluate", remarkMap.get(chart.getName()) == null ? "": remarkMap.get(chart.getName()));
            chartImages.add(chartMap);
            content.put("chartImages", chartImages);
            analysisContents.add(content);
        }
        model.put("analysisContents", analysisContents);

        //4.1.3.1	{{tabName1}}安全现状
        List<Map<String, Object>> verification = Lists.newArrayList();
        List<Integer> stantardIdList = Arrays.asList(1001,1027);
        List<String> stantardFileNameList = Arrays.asList("个人信息安全规范","汽车数据处理安全要求");

        for (int i=0; i<stantardFileNameList.size(); i++) {
            AtomicInteger sort = new AtomicInteger(1);
            Map<String, Object> map = new HashMap<>();
            map.put("tabName", stantardFileNameList.get(i));
            int finalI = i;
            map.put("tabList", coVerifications.stream().filter(v -> Objects.equals(v.getStandardId(), stantardIdList.get(
                    finalI)))
                .sorted(Comparator.comparing(CoVerification::getBpCode)).peek(s -> {
                    s.setSort(sort.getAndIncrement());
                    s.setDescription(s.getDesc());
                }).collect(Collectors.toList()));
            verification.add(map);
        }
        model.put("verification", verification);
    }

    @Override
    public Set<LabelEnum> getNeedExcludeContent() {
        return CollUtil.newHashSet();
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.IOV;
    }
}
