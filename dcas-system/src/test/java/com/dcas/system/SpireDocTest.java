package com.dcas.system;

import com.spire.doc.Document;
import com.spire.doc.FileFormat;
import com.spire.doc.HeaderFooter;
import com.spire.doc.Section;
import com.spire.doc.documents.ShapeType;
import com.spire.doc.documents.ShapeLineStyle;
import com.spire.doc.fields.ShapeObject;
import org.junit.Test;

/**
 * Spire.Doc.Free 测试类
 */
public class SpireDocTest {

    @Test
    public void testSpireDocWatermark() {
        try {
            // 创建一个新文档
            Document document = new Document();
            
            // 添加一个节
            Section section = document.addSection();
            
            // 添加一个段落
            section.addParagraph().appendText("这是一个测试文档");
            
            // 获取页眉
            HeaderFooter header = section.getHeadersFooters().getHeader();
            
            // 创建水印形状
            ShapeObject watermark = new ShapeObject(document, ShapeType.Text_Plain_Text);
            
            // 设置水印大小
            watermark.setWidth(120);
            watermark.setHeight(30);
            
            // 设置水印位置和旋转
            watermark.setVerticalPosition(100);
            watermark.setHorizontalPosition(100);
            watermark.setRotation(315); // 旋转-45度
            
            // 设置水印文字
            watermark.getWordArt().setText("内部机密");
            
            // 设置水印样式
            watermark.setFillColor(new java.awt.Color(192, 192, 192, 100)); // 半透明灰色
            watermark.setLineStyle(ShapeLineStyle.Single);
            watermark.setStrokeColor(new java.awt.Color(192, 192, 192, 100));
            watermark.setStrokeWeight(1);
            
            // 将水印添加到页眉
            header.addParagraph().getChildObjects().add(watermark);
            
            // 保存文档到临时文件
            String tempPath = System.getProperty("java.io.tmpdir") + "test_watermark.docx";
            document.saveToFile(tempPath, FileFormat.Docx_2013);
            document.dispose();
            
            System.out.println("测试成功，文档已保存到: " + tempPath);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("测试失败: " + e.getMessage());
        }
    }
}
